<template>
  <div class="dashboard">
    <!-- 头部区域 -->
    <header class="header">
      <div class="left-stats">
        <circle-progress title="完成率" :percentage="75" />
        <circle-progress title="增长率" :percentage="65" />
      </div>
      <div class="center-title">
        <h1>数据可视化监控平台</h1>
        <div class="digital-counter">
          <counter-number
            :value="orderTotal"
            unit="元"
            title="订单总额实时展示"
          />
        </div>
      </div>
      <div class="right-stats">
        <trend-chart title="销售趋势" :data="salesData" />
      </div>
    </header>

    <!-- 中间区域 -->
    <main class="main-content">
      <div class="left-charts">
        <line-chart title="订单走势" :data="orderData" />
        <pie-chart title="业务占比" :data="businessData" />
      </div>
      <div class="globe-visualization">
        <div class="panel-header">
          <h3>全球数据流向</h3>
        </div>
        <div class="panel-content">
          <globe-visualization :data="globeData" />
        </div>
      </div>
      <div class="right-charts">
        <pie-chart title="平台占比" :data="platformData" />
        <line-chart title="产品趋势" :data="productData" />
      </div>
    </main>

    <!-- 底部区域 -->
    <footer class="footer">
      <div class="data-table">
        <data-table title="主要城市" :data="cityData" />
      </div>
      <div class="bar-chart">
        <bar-chart title="重点城市" :data="keyPointData" />
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import CircleProgress from "../components/CircleProgress.vue";
import CounterNumber from "../components/CounterNumber.vue";
import TrendChart from "../components/TrendChart.vue";
import LineChart from "../components/LineChart.vue";
import PieChart from "../components/PieChart.vue";
import GlobeVisualization from "../components/GlobeVisualization.vue";
import DataTable from "../components/DataTable.vue";
import BarChart from "../components/BarChart.vue";
import { fetchDashboardData } from "../api/dashboard";

// 数据定义
const salesData = ref([]);
const orderData = ref([]);
const businessData = ref([]);
const globeData = ref([]);
const platformData = ref([]);
const productData = ref([]);
const cityData = ref([]);
const keyPointData = ref([]);
const orderTotal = ref(11119);

// 模拟订单总额实时增长
let orderTimer = null;

function startOrderSimulation() {
  orderTimer = setInterval(() => {
    // 随机增加一个金额
    const increment = Math.floor(Math.random() * 500) + 100;
    orderTotal.value += increment;
  }, 5000); // 每5秒更新一次
}

// 获取数据
onMounted(async () => {
  try {
    const data = await fetchDashboardData();
    salesData.value = data.salesData;
    orderData.value = data.orderData;
    businessData.value = data.businessData;
    globeData.value = data.globeData;
    platformData.value = data.platformData;
    productData.value = data.productData;
    cityData.value = data.cityData;
    keyPointData.value = data.keyPointData;

    // 启动订单总额模拟
    startOrderSimulation();
  } catch (error) {
    console.error("获取数据失败:", error);
    // 即使获取数据失败，也启动订单总额模拟
    startOrderSimulation();
  }
});

onBeforeUnmount(() => {
  // 清除定时器
  if (orderTimer) {
    clearInterval(orderTimer);
  }
});
</script>

<style scoped>
.dashboard {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.header {
  display: flex;
  height: 20%;
  margin-bottom: 20px;
}

.left-stats,
.right-stats {
  width: 25%;
  display: flex;
  flex-direction: column;
}

.center-title {
  width: 50%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.digital-counter {
  width: 80%;
  margin-top: 10px;
  padding: 15px;
  background-color: rgba(0, 20, 80, 0.3);
  border-radius: 10px;
  border: 1px solid rgba(0, 228, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 228, 255, 0.3);
}

.main-content {
  display: flex;
  height: 60%;
  margin-bottom: 20px;
}

.left-charts,
.right-charts {
  width: 25%;
  display: flex;
  flex-direction: column;
}

.globe-visualization {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.panel-header {
  width: 100%;
  padding: 10px;
  background-color: rgba(0, 20, 80, 0.3);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border: 1px solid rgba(0, 228, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 228, 255, 0.3);
}

.panel-content {
  width: 100%;
  height: 800px;
  background-color: rgba(0, 20, 80, 0.3);
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border: 1px solid rgba(0, 228, 255, 0.3);
  box-shadow: 0 0 15px rgba(0, 228, 255, 0.3);
}

.footer {
  display: flex;
  height: 20%;
}

.data-table,
.bar-chart {
  width: 50%;
}

h1 {
  color: #00ffff;
  font-size: 2.5rem;
  margin-bottom: 20px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.7);
}
</style>
