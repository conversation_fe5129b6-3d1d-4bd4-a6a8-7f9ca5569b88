<template>
  <div class="chart-container">
    <div class="chart-title">{{ title }}</div>
    <div class="chart" ref="chartRef"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: '饼图'
  },
  data: {
    type: Array,
    default: () => []
  }
})

const chartRef = ref(null)
let chart = null

function initChart() {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  updateChart()
}

function updateChart() {
  if (!chart) return
  
  const defaultData = [
    { name: '类别1', value: 35 },
    { name: '类别2', value: 25 },
    { name: '类别3', value: 20 },
    { name: '类别4', value: 20 }
  ]
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      textStyle: {
        color: 'rgba(255, 255, 255, 0.7)'
      }
    },
    series: [
      {
        name: props.title,
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#030929',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '12',
            fontWeight: 'bold',
            color: '#fff'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.length ? props.data : defaultData,
        color: ['#00e4ff', '#0066ff', '#00ffcc', '#ffcc00', '#ff6666']
      }
    ]
  }
  
  chart.setOption(option)
}

function resizeChart() {
  chart && chart.resize()
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeChart)
})

onBeforeUnmount(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', resizeChart)
})

watch(() => props.data, () => {
  updateChart()
}, { deep: true })
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: rgba(0, 20, 80, 0.3);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(0, 200, 255, 0.2);
}

.chart-title {
  font-size: 16px;
  color: #00e4ff;
  margin-bottom: 10px;
  text-align: center;
}

.chart {
  width: 100%;
  height: calc(100% - 30px);
}
</style>